package com.zsmall.system.controller;

import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.validate.AddGroup;
import com.hengjian.common.core.validate.EditGroup;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.web.core.BaseController;
import com.zsmall.system.biz.service.IAnnouncementService;
import com.zsmall.system.entity.domain.bo.announcement.AnnouncementBo;
import com.zsmall.system.entity.domain.dto.announcement.BatchReadStatusDto;
import com.zsmall.system.entity.domain.vo.announcement.AnnouncementVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 公告Controller
 * 包含管理员端和用户端的所有接口
 *
 * <AUTHOR> Assistant
 * @date 2024-12-27
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/system/announcement")
public class AnnouncementController extends BaseController {

    private final IAnnouncementService announcementService;


    /**
     * 分页查询公告列表（通用接口）
     * 管理员：查看所有公告（包含已禁用、已过期的公告，用于管理）
     * 分销商/供应商：查看适用于自己租户类型的有效公告（仅启用且未过期）
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @param includeReadStatus 是否包含已读状态信息（可选，默认false）
     * @param isRead 已读状态筛选（可选，仅当includeReadStatus=true时有效）
     */
    @GetMapping("/list")
    public TableDataInfo<?> list(AnnouncementBo bo,
                                PageQuery pageQuery,
                                @RequestParam(required = false, defaultValue = "false") Boolean includeReadStatus,
                                @RequestParam(required = false) Integer isRead) {
        String tenantType = LoginHelper.getTenantType();
        String tenantId = LoginHelper.getTenantId();

        // 如果不是管理员，则只查询适用于当前租户类型的公告
        if (!"Manager".equals(tenantType)) {
            bo.setTenantType(tenantType);
        }

        // 如果需要包含已读状态信息（通常是分销商/供应商查询消息列表）
        if (includeReadStatus && StrUtil.isNotEmpty(tenantId) && !"Manager".equals(tenantType)) {
            return announcementService.queryMessageList(tenantId, tenantType, isRead, pageQuery);
        } else {
            return announcementService.queryPageList(bo, pageQuery);
        }
    }

    /**
     * 获取公告详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<AnnouncementVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(announcementService.queryById(id));
    }

    /**
     * 新增公告
     */
    @Log(title = "公告", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    @Transactional(rollbackFor = Exception.class)
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AnnouncementBo bo) {
        return toAjax(announcementService.insertByBo(bo));
    }

    /**
     * 修改公告
     */
    @Log(title = "公告", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AnnouncementBo bo) {
        return toAjax(announcementService.updateByBo(bo));
    }

    /**
     * 删除公告（智能删除）
     * 管理员：所有租户不可见（全局删除）
     * 分销商/供应商：仅对当前租户不可见（租户级删除）
     */
    @Log(title = "公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        String tenantType = LoginHelper.getTenantType();
        String tenantId = LoginHelper.getTenantId();

        if (StrUtil.isEmpty(tenantId)) {
            return R.fail("请登录后使用");
        }

        if ("Manager".equals(tenantType)) {
            // 管理员删除：全局删除，所有租户不可见
            return toAjax(announcementService.deleteWithValidByIds(Arrays.asList(ids)));
        } else {
            // 分销商/供应商删除：仅对当前租户不可见
            return toAjax(announcementService.deleteForTenant(Arrays.asList(ids), tenantId));
        }
    }

    /**
     * 更新公告状态（启用/停用）
     */
    @Log(title = "公告状态", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public R<Void> updateStatus(@RequestBody AnnouncementBo bo) {
        if (bo.getId() == null || bo.getStatus() == null) {
            return R.fail("参数不能为空");
        }
        return toAjax(announcementService.updateStatus(bo.getId(), bo.getStatus()));
    }


    /**
     * 批量更新已读状态
     * @param dto 包含公告ID列表和已读状态的请求对象
     */
    @PutMapping("/messages/batch-read-status")
    public R<Void> batchUpdateReadStatus(@Validated @RequestBody BatchReadStatusDto dto) {
        String tenantId = LoginHelper.getTenantId();
        if (StrUtil.isEmpty(tenantId)) {
            return R.fail("请登录后使用");
        }

        if (dto.getIsRead() == 1) {
            return toAjax(announcementService.batchMarkRead(dto.getAnnouncementIds(), tenantId));
        } else {
            return toAjax(announcementService.batchMarkUnread(dto.getAnnouncementIds(), tenantId));
        }
    }

}
